#!/usr/bin/env python3
"""
Script to analyze how clusters are categorized in the Neo4j database.
Shows all cluster labels and their properties to understand knowledge vs non-knowledge categorization.
"""

import os
import sys
from pathlib import Path
import yaml
from neo4j import GraphDatabase

# Add project to path
sys.path.insert(0, str(Path(__file__).parent / 'seeding_pipeline'))

def load_podcast_configs():
    """Load podcast configurations from YAML file."""
    config_path = Path(__file__).parent / "seeding_pipeline" / "config" / "podcasts.yaml"
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return {p['id']: p for p in config.get('podcasts', [])}

def get_db_driver(podcast_id):
    """Get database connection for specific podcast."""
    configs = load_podcast_configs()
    
    if podcast_id not in configs:
        # Fallback to default if podcast not found
        uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
        password = os.environ.get('NEO4J_PASSWORD', 'password')
        return GraphDatabase.driver(uri, auth=("neo4j", password))
    
    podcast_config = configs[podcast_id]
    db_config = podcast_config.get('database', {})
    uri = db_config.get('uri', 'bolt://localhost:7687')
    password = db_config.get('neo4j_password', 'password')
    
    return GraphDatabase.driver(uri, auth=("neo4j", password))

def analyze_cluster_categories(podcast_id):
    """Analyze cluster categories and labels in the database."""
    driver = get_db_driver(podcast_id)
    
    try:
        with driver.session() as session:
            print(f"\n=== Analyzing clusters for podcast: {podcast_id} ===\n")
            
            # 1. Get all clusters with their properties
            print("1. All Clusters and Their Properties:")
            print("-" * 80)
            clusters_query = """
            MATCH (c:Cluster)
            RETURN c.id as id, c.label as label, c.status as status, 
                   c.member_count as member_count,
                   keys(c) as all_properties
            ORDER BY c.member_count DESC
            """
            
            result = session.run(clusters_query)
            clusters = list(result)
            
            if not clusters:
                print("No clusters found in the database.")
                return
                
            for record in clusters:
                print(f"ID: {record['id']}")
                print(f"Label: {record['label']}")
                print(f"Status: {record['status']}")
                print(f"Member Count: {record['member_count']}")
                print(f"All Properties: {', '.join(record['all_properties'])}")
                print("-" * 40)
            
            # 2. Check for filtered clusters (non-knowledge)
            print("\n2. Clusters Filtered Out as Non-Knowledge:")
            print("-" * 80)
            filtered_labels = ['Outro', 'Intro', 'Advertisement', 'Sponsor']
            filtered_query = """
            MATCH (c:Cluster)
            WHERE c.label IN $labels
            RETURN c.id as id, c.label as label, c.member_count as member_count
            ORDER BY c.label
            """
            
            result = session.run(filtered_query, labels=filtered_labels)
            filtered_clusters = list(result)
            
            if filtered_clusters:
                for record in filtered_clusters:
                    print(f"- {record['label']} (ID: {record['id']}, Members: {record['member_count']})")
            else:
                print("No clusters found with non-knowledge labels.")
            
            # 3. Check for any type or category properties
            print("\n3. Checking for Type/Category Properties:")
            print("-" * 80)
            type_query = """
            MATCH (c:Cluster)
            WHERE exists(c.type) OR exists(c.category) OR exists(c.is_knowledge)
            RETURN c.id as id, c.label as label, 
                   c.type as type, c.category as category, c.is_knowledge as is_knowledge
            """
            
            result = session.run(type_query)
            typed_clusters = list(result)
            
            if typed_clusters:
                for record in typed_clusters:
                    print(f"ID: {record['id']}, Label: {record['label']}")
                    if record['type']:
                        print(f"  Type: {record['type']}")
                    if record['category']:
                        print(f"  Category: {record['category']}")
                    if record['is_knowledge'] is not None:
                        print(f"  Is Knowledge: {record['is_knowledge']}")
            else:
                print("No clusters found with type, category, or is_knowledge properties.")
            
            # 4. Analyze label patterns
            print("\n4. Cluster Label Patterns:")
            print("-" * 80)
            label_analysis_query = """
            MATCH (c:Cluster)
            WITH c.label as label, count(*) as count
            RETURN label, count
            ORDER BY count DESC, label
            """
            
            result = session.run(label_analysis_query)
            label_counts = list(result)
            
            for record in label_counts:
                print(f"{record['label']}: {record['count']} cluster(s)")
            
            # 5. Sample meaningful units from filtered clusters
            print("\n5. Sample Content from Potentially Non-Knowledge Clusters:")
            print("-" * 80)
            for label in filtered_labels:
                sample_query = """
                MATCH (c:Cluster {label: $label})
                MATCH (m:MeaningfulUnit)-[:IN_CLUSTER]->(c)
                RETURN c.label as cluster_label, 
                       collect(m.summary)[..3] as sample_summaries
                LIMIT 1
                """
                
                result = session.run(sample_query, label=label)
                record = result.single()
                
                if record:
                    print(f"\n{record['cluster_label']}:")
                    for i, summary in enumerate(record['sample_summaries'], 1):
                        print(f"  {i}. {summary[:100]}..." if len(summary) > 100 else f"  {i}. {summary}")
            
    except Exception as e:
        print(f"Error analyzing clusters: {e}")
    finally:
        driver.close()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Analyze cluster categories in Neo4j database")
    parser.add_argument("podcast_id", help="Podcast ID to analyze")
    args = parser.parse_args()
    
    analyze_cluster_categories(args.podcast_id)
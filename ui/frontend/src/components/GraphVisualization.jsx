import React, { useState, useEffect, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import ForceGraph3D from 'react-force-graph-3d'

function GraphVisualization({ podcastId }) {
  const navigate = useNavigate()
  const [graphData, setGraphData] = useState({ nodes: [], links: [] })
  const [viewState, setViewState] = useState({
    loading: true,
    mode: 'clusters',
    selectedNode: null,
    clusterId: null,
    clusterLabel: null
  })
  const fgRef = useRef()

  useEffect(() => {
    fetchGraphData()
  }, [podcastId, viewState.mode, viewState.clusterId])

  // Handle window resize to maintain proper aspect ratio
  useEffect(() => {
    const handleResize = () => {
      if (fgRef.current) {
        fgRef.current.width(window.innerWidth)
        fgRef.current.height(window.innerHeight)
      }
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Initialize graph settings for optimal rendering
  useEffect(() => {
    if (fgRef.current && !viewState.loading) {
      const fg = fgRef.current

      // Ensure proper aspect ratio and rendering
      fg.renderer().setPixelRatio(window.devicePixelRatio || 1)
    }
  }, [viewState.loading, graphData])

  // Auto-fit graph to viewport after data loads
  useEffect(() => {
    if (fgRef.current && graphData.nodes.length > 0 && !viewState.loading) {
      // Longer delay to ensure force simulation has settled
      setTimeout(() => {
        fgRef.current.zoomToFit(400, 50)
      }, 500)
    }
  }, [graphData, viewState.loading])

  const fetchGraphData = async () => {
    try {
      // Use the new consolidated /graph endpoint
      let url = `/api/v1/podcasts/${podcastId}/graph?view=${viewState.mode}`
      if (viewState.mode === 'units' && viewState.clusterId) {
        url += `&cluster_id=${viewState.clusterId}`
      }
      
      const response = await fetch(url)
      if (!response.ok) throw new Error('Failed to fetch graph data')
      const data = await response.json()
      
      setGraphData(data)
      if (data.cluster_label) {
        setViewState(prev => ({ ...prev, clusterLabel: data.cluster_label }))
      }
    } catch (err) {
      console.error('Error fetching graph data:', err)
    } finally {
      setViewState(prev => ({ ...prev, loading: false }))
    }
  }

  const handleNodeClick = (node) => {
    if (viewState.mode === 'clusters') {
      // In clusters view, clicking a cluster drills down to units
      setViewState(prev => ({
        ...prev,
        clusterId: node.id,
        mode: 'units',
        selectedNode: null,
        loading: true
      }))
    } else {
      // In units view, clicking shows node details
      setViewState(prev => ({ ...prev, selectedNode: node }))
    }
  }

  const handleBackClick = () => {
    if (viewState.mode === 'units') {
      // Go back to clusters view
      setViewState({
        loading: true,
        mode: 'clusters',
        selectedNode: null,
        clusterId: null,
        clusterLabel: null
      })
    } else {
      // Go back to dashboard
      navigate('/')
    }
  }

  if (viewState.loading) return <div style={{ color: '#a0a0a0' }}>Loading graph...</div>

  return (
    <div style={{ width: '100%', height: '100%', position: 'relative' }}>
      <ForceGraph3D
        ref={fgRef}
        graphData={graphData}
        nodeLabel="name"
        nodeAutoColorBy={viewState.mode === 'clusters' ? 'id' : 'cluster'}
        onNodeClick={handleNodeClick}
        linkOpacity={0.2}
        linkColor={link => link.color || '#ffffff'}
        linkWidth={link => (link.width || 4) * 0.75}
        nodeOpacity={0.9}
        backgroundColor="#000011"
        width={window.innerWidth}
        height={window.innerHeight}
        nodeVal={node => {
          if (viewState.mode === 'clusters') {
            // Scale based on member count with reasonable bounds
            const memberCount = node.member_count || 1
            return Math.max(4, Math.min(20, 4 + Math.log(memberCount) * 2))
          }
          return 4  // Uniform size for units
        }}
        nodeResolution={16}
        enableNodeDrag={false}
        enableNavigationControls={true}
        showNavInfo={false}
        cooldownTime={1500}
        d3AlphaDecay={0.02}
        d3VelocityDecay={0.3}
        chargeStrength={-30}
      />
      <button
        onClick={handleBackClick}
        style={{
          position: 'absolute',
          top: '20px',
          left: '20px',
          background: 'rgba(0, 0, 0, 0.5)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          color: '#fff',
          padding: '8px 16px',
          borderRadius: '4px',
          cursor: 'pointer',
          fontSize: '14px',
          zIndex: 1000
        }}
      >
        ← {viewState.mode === 'units' ? 'Back to Clusters' : 'Back to Dashboard'}
      </button>
      {viewState.mode === 'units' && viewState.clusterLabel && (
        <div style={{ 
          position: 'absolute', 
          top: '20px', 
          left: '50%', 
          transform: 'translateX(-50%)',
          background: 'rgba(0, 0, 0, 0.5)',
          padding: '10px 20px',
          borderRadius: '5px',
          color: '#fff',
          fontSize: '18px',
          fontWeight: 'bold'
        }}>
          {viewState.clusterLabel}
        </div>
      )}
      {viewState.selectedNode && viewState.mode === 'units' && (
        <div style={{
          position: 'absolute',
          top: '20px',
          right: '20px',
          background: 'rgba(0, 0, 0, 0.8)',
          padding: '10px',
          borderRadius: '5px',
          color: '#fff',
          maxWidth: '300px',
          zIndex: 1000
        }}>
          <h4 style={{ margin: '0 0 10px 0' }}>Selected Unit</h4>
          <p style={{ margin: 0 }}>{viewState.selectedNode.name}</p>
          {viewState.selectedNode.themes && (
            <p style={{ margin: '5px 0 0 0', fontSize: '0.9em', color: '#a0a0a0' }}>
              Themes: {viewState.selectedNode.themes}
            </p>
          )}
        </div>
      )}
    </div>
  )
}

export default GraphVisualization
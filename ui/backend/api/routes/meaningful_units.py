from fastapi import APIRouter, HTTPException, Query
from neo4j import GraphDatabase
import os
import yaml
from pathlib import Path
from typing import Dict, List, Any, Optional

router = APIRouter()

# Cache for podcast configurations
_podcast_configs = None

def load_podcast_configs():
    """Load podcast configurations from YAML file."""
    global _podcast_configs
    if _podcast_configs is None:
        config_path = Path(__file__).parent.parent.parent.parent.parent / "seeding_pipeline" / "config" / "podcasts.yaml"
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        _podcast_configs = {p['id']: p for p in config.get('podcasts', [])}
    return _podcast_configs

# Database connection per podcast
def get_db_driver(podcast_id: str):
    """Get database connection for specific podcast."""
    configs = load_podcast_configs()
    
    if podcast_id not in configs:
        # Fallback to default if podcast not found
        uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
        password = os.environ.get('NEO4J_PASSWORD', 'password')
        return GraphDatabase.driver(uri, auth=("neo4j", password))
    
    podcast_config = configs[podcast_id]
    db_config = podcast_config.get('database', {})
    uri = db_config.get('uri', 'bolt://localhost:7687')
    password = db_config.get('neo4j_password', 'password')
    
    return GraphDatabase.driver(uri, auth=("neo4j", password))

@router.get("/podcasts/{podcast_id}/graph")
async def get_podcast_graph(
    podcast_id: str,
    view: str = Query(default="clusters", description="View type: 'clusters' or 'units'"),
    cluster_id: Optional[str] = Query(default=None, description="Cluster ID for units view")
) -> Dict[str, Any]:
    """Single flexible endpoint for all graph data."""
    
    driver = get_db_driver(podcast_id)
    
    try:
        with driver.session() as session:
            if view == "clusters":
                return _get_clusters_data(session)
            else:
                if not cluster_id:
                    raise HTTPException(status_code=400, detail="cluster_id required for units view")
                return _get_units_data(session, cluster_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        driver.close()

def _get_clusters_data(session):
    """Get clusters view data."""
    # Simple clusters query - exclude non-knowledge clusters
    clusters_query = """
    MATCH (c:Cluster)
    WHERE c.status = 'active' 
    AND NOT (
        toLower(c.label) CONTAINS 'outro' OR
        toLower(c.label) CONTAINS 'intro' OR
        toLower(c.label) CONTAINS 'advertis' OR
        toLower(c.label) CONTAINS 'sponsor' OR
        toLower(c.label) CONTAINS 'promotion'
    )
    RETURN c.id as id, c.label as label, c.member_count as member_count
    ORDER BY c.member_count DESC
    """
    
    clusters_result = session.run(clusters_query)
    cluster_records = list(clusters_result)
    
    # Debug: log how many clusters were found
    print(f"Found {len(cluster_records)} clusters after filtering")
    
    # Build nodes
    nodes = []
    for record in cluster_records:
        nodes.append({
            "id": record["id"],
            "name": record["label"],
            "label": record["label"],
            "member_count": record["member_count"]
        })
    
    # Find cluster connections through shared entities
    links_query = """
    MATCH (c1:Cluster)<-[:IN_CLUSTER]-(m1:MeaningfulUnit)-[:PART_OF]->(ep:Episode)<-[:MENTIONED_IN]-(e:Entity)
    MATCH (e)-[:MENTIONED_IN]->(ep2:Episode)<-[:PART_OF]-(m2:MeaningfulUnit)-[:IN_CLUSTER]->(c2:Cluster)
    WHERE c1.id < c2.id
    AND c1.status = 'active' AND c2.status = 'active'
    AND NOT (
        toLower(c1.label) CONTAINS 'outro' OR
        toLower(c1.label) CONTAINS 'intro' OR
        toLower(c1.label) CONTAINS 'advertis' OR
        toLower(c1.label) CONTAINS 'sponsor' OR
        toLower(c1.label) CONTAINS 'promotion'
    )
    AND NOT (
        toLower(c2.label) CONTAINS 'outro' OR
        toLower(c2.label) CONTAINS 'intro' OR
        toLower(c2.label) CONTAINS 'advertis' OR
        toLower(c2.label) CONTAINS 'sponsor' OR
        toLower(c2.label) CONTAINS 'promotion'
    )
    RETURN DISTINCT c1.id as source, c2.id as target
    """
    
    links_result = session.run(links_query)
    links = []
    for record in links_result:
        links.append({
            "source": record["source"],
            "target": record["target"],
            "color": "#ffffff",
            "width": 3
        })
    
    return {"nodes": nodes, "links": links}

def _get_units_data(session, cluster_id: str):
    """Get units view data for a specific cluster."""
    query = """
    MATCH (c:Cluster {id: $cluster_id})
    MATCH (m:MeaningfulUnit)-[:IN_CLUSTER]->(c)
    WITH m, c
    ORDER BY m.id
    WITH c, collect({
        id: m.id,
        summary: m.summary,
        themes: m.themes
    }) as units
    RETURN c.label as cluster_label, units
    """
    
    result = session.run(query, cluster_id=cluster_id)
    record = result.single()
    
    if not record:
        raise HTTPException(status_code=404, detail=f"Cluster {cluster_id} not found")
    
    units_data = record["units"]
    cluster_label = record["cluster_label"]
    
    # Build nodes
    nodes = []
    for unit in units_data:
        nodes.append({
            "id": unit["id"],
            "name": unit["summary"][:100] + "..." if unit["summary"] and len(unit["summary"]) > 100 else unit["summary"] or "No summary",
            "cluster": cluster_id,
            "themes": unit["themes"]
        })
    
    # Simple mesh links within cluster
    links = []
    if len(units_data) > 1:
        for i in range(len(units_data)):
            for j in range(i + 1, len(units_data)):
                links.append({
                    "source": units_data[i]["id"],
                    "target": units_data[j]["id"],
                    "color": "#ffffff",
                    "width": 2.25
                })
    
    return {
        "nodes": nodes,
        "links": links,
        "cluster_label": cluster_label
    }